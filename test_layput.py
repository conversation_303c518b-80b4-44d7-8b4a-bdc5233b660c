import os
import subprocess
import tempfile

from graphviz import Graph


def layout_nodes(rectangles, fixed_id=None, min_sep=0.5, scale=96):
    """
    rectangles: list of dicts {id, x, y, w, h} in pixels
    fixed_id: node id that should remain fixed
    min_sep: minimal separation between nodes (inches)
    scale: pixels per inch of input coordinates (default=96)

    Returns: list of dicts {id, x, y, w, h} with updated positions in same unit as input
    """

    dot = Graph("layout", engine="neato")
    dot.attr(overlap="false", sep=str(min_sep))

    for rect in rectangles:
        node_id = rect["id"]

        # Convert width/height: pixels → inches
        w_in, h_in = rect["w"] / scale, rect["h"] / scale

        # Center coords: pixels → inches, then → points (1 inch = 72 points)
        cx_pt = (rect["x"] + rect["w"] / 2) / scale * 72
        cy_pt = (rect["y"] + rect["h"] / 2) / scale * 72

        pos_str = f"{cx_pt},{cy_pt}"
        if node_id == fixed_id:
            print(f"Fixed node {node_id} at {pos_str}")
            pos_str += "!"

        dot.node(
            node_id,
            shape="rect",
            width=str(w_in),
            height=str(h_in),
            fixedsize="true",
            pos=pos_str,
        )

    # Save DOT file
    with tempfile.NamedTemporaryFile(delete=False, suffix=".dot") as f:
        dot_path = f.name
        f.write(dot.source.encode("utf-8"))

    # Run neato with -n2 (preserve fixed positions)
    cmd = ["neato", "-n2", "-Tplain", dot_path]
    result = subprocess.run(cmd, capture_output=True, text=True, check=True)
    os.remove(dot_path)

    results = []
    for line in result.stdout.splitlines():
        parts = line.split()
        if parts[0] == "node":
            node_id = parts[1]

            # plain output is in inches
            cx_in, cy_in = float(parts[2]), float(parts[3])
            rect = next(r for r in rectangles if r["id"] == node_id)

            # Convert inches → pixels
            cx_px = cx_in * scale
            cy_px = cy_in * scale

            new_x = cx_px - rect["w"] / 2
            new_y = cy_px - rect["h"] / 2  # flip if needed

            results.append(
                {"id": node_id, "x": new_x, "y": new_y, "w": rect["w"], "h": rect["h"]}
            )

    return results


# Example usage
rects = [
    {"id": "A", "x": 0, "y": 0, "w": 200, "h": 100},  # fixed node in pixels
    {"id": "B", "x": 300, "y": 200, "w": 150, "h": 100},
    {"id": "C", "x": 500, "y": 100, "w": 100, "h": 150},
]

updated = layout_nodes(rects, fixed_id="A", min_sep=1)
for r in updated:
    print(r)
